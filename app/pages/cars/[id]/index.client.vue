<template>
  <UDashboardPanel id="home">
    <template #header>
      <UDashboardNavbar>
        <template #leading>
          <UButton
            @click="navigateTo('/cars')"
            variant="outline"
            color="neutral"
            icon="i-heroicons-chevron-left"
          >
            戻る
          </UButton>
        </template>
        <template #right>
          <!-- Delete Button -->
          <UModal title="削除">
            <UButton icon="i-heroicons-trash" variant="outline" color="error">
              削除
            </UButton>
            <template #body class="p-4">
              <p class="text-lg font-bold">この車両を削除しますか？</p>
              <p>操作は取り消せません</p>
              <UButton
                @click="deleteCar"
                color="error"
                block
                class="mt-4"
                :loading="isLoading"
                >はい</UButton
              >
            </template>
          </UModal>

          <!-- Edit Button -->
          <UButton
            :to="{ name: 'cars-id-edit', params: { id: route.params.id } }"
            icon="i-heroicons-pencil-square"
            variant="outline"
          >
            編集
          </UButton>
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <div>
        <h2 class="text-3xl font-bold">{{ car?.car_name }}</h2>
        <h3 class="text-muted">{{ car?.maker }} {{ car?.model }}</h3>
      </div>
      <div class="grid xl:grid-cols-2 mb-12 gap-4">
        <div id="car-detail" v-if="car" class="max-w-2xl mx-auto">
          <div id="image-carousel">
            <UCarousel
              ref="carousel"
              v-slot="{ item }"
              wheel-gestures
              :items="images"
              :prev="{ onClick: onClickPrev }"
              :next="{ onClick: onClickNext }"
              class="w-full mx-auto grow"
              @select="onSelect"
            >
              <img
                :src="item"
                class="rounded cursor-pointer w-full object-cover"
                loading="lazy"
                @click="navigateTo({ name: 'cars-id', params: { id: car.id } })"
              />
            </UCarousel>

            <div class="flex gap-1 justify-center w-full my-2">
              <div
                v-for="(item, index) in images"
                :key="index"
                class="opacity-50 hover:opacity-100 transition-opacity"
                :class="{ 'opacity-100': activeIndex === index }"
                @click="select(index)"
              >
                <img
                  :src="item"
                  class="max-h-16 aspect-square object-cover rounded cursor-pointer"
                />
              </div>
            </div>

            <VueEasyLightbox
              :visible="visibleRef"
              :imgs="images"
              :index="indexRef"
              @hide="visibleRef = false"
            />
          </div>

          <div id="car-info" class="flex flex-col gap-2 w-full">
            <div
              id="details-section"
              class="grid gap-4 grid-cols-1 lg:grid-cols-12 h-full"
            >
              <div class="col-span-4 h-full flex flex-col">
                <p class="font-semibold">車体本体価格</p>
                <p class="text-4xl text-error font-bold">
                  <template v-if="!car.price && car.price !== 0">-</template>
                  <template v-else-if="car.price === 0"> 応談 </template>
                  <template v-else>
                    {{ formatNumber(car.price) }}
                    <span class="text-sm text-black">万円</span>
                  </template>
                </p>

                <nuxt-link
                  :to="car.source_url ?? '#'"
                  external
                  target="_blank"
                  class="hidden lg:block mt-auto text-dimmed text-xs line-clamp-2 break-all"
                  >取得元: {{ displayValue(car.source) !== '-' ? displayValue(car.source) : displayValue(car.source_url) }}</nuxt-link
                >
                
                <!-- メタデータ: 作成日・更新日 -->
                <div class="mt-3 space-y-1 text-xs text-dimmed">
                  <div v-if="!car" class="text-gray-400">
                    データ読み込み中...
                  </div>
                  <div v-else-if="car?.created_at" class="flex items-center">
                    <span class="font-medium text-gray-600 w-16">作成日:</span>
                    <span class="text-gray-800">{{ formatDate(car.created_at) }}</span>
                  </div>
                  <div v-else class="flex items-center">
                    <span class="font-medium text-gray-600 w-16">作成日:</span>
                    <span class="text-red-500">データなし</span>
                  </div>
                  <div v-if="car?.scraped_at" class="flex items-center">
                    <span class="font-medium text-gray-600 w-16">更新日:</span>
                    <span class="text-gray-800">{{ formatDate(car.scraped_at) }}</span>
                  </div>
                  <div v-else class="flex items-center">
                    <span class="font-medium text-gray-600 w-16">更新日:</span>
                    <span class="text-red-500">データなし</span>
                  </div>
                </div>


              </div>
              <div id="detail-item-wrapper" class="grid grid-cols-2 col-span-8">
                <div class="flex flex-col gap-2 text-xs font-bold">
                  <DetailItem
                    title="年式"
                    :value="
                      car.model_year ? String(car.model_year).slice(0, 4) : '-'
                    "
                  />
                  <DetailItem
                    title="車検"
                    :value="displayValue(car.vehicle_inspection)"
                  />
                  <DetailItem title="保険" :value="displayValue(car.insurance)" />
                  <DetailItem title="排気量" :value="car.displacement ? `${formatNumber(car.displacement)}cc` : '-'" />
                  <DetailItem title="走行距離" :value="car.mileage ? `${formatNumber(car.mileage)}km` : '-'" />
                  <DetailItem title="ミッション" :value="displayValue(car.mission)" />
                </div>
                <div class="flex flex-col gap-2 text-xs font-bold">
                  <DetailItem title="モデル" :value="displayValue(car.model)" />
                  <DetailItem title="ハンドル" :value="displayValue(car.handle)" />
                  <DetailItem
                    title="エンジン"
                    :value="displayValue(car.engine_type)"
                  />
                  <DetailItem
                    title="駆動方式"
                    :value="displayValue(car.driving_system)"
                  />
                  <DetailItem title="修復歴" :value="displayValue(car.fix_history)" />
                  <DetailItem title="整備" :value="displayValue(car.maintenance)" />
                </div>
              </div>
            </div>

            <!-- Detail Section -->
            <div v-if="car.detail" id="car-detail-description" class="mt-6 p-4 bg-gray-50 rounded-lg">
              <h3 class="text-lg font-semibold mb-3">詳細</h3>
              <p class="text-sm leading-relaxed whitespace-pre-wrap">{{ car.detail }}</p>
            </div>
          </div>
        </div>
        <div id="charts" class="mt-8">
          <HistoricalCarPriceChart v-if="car" :car />
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>

<script lang="ts" setup>
const route = useRoute("cars-id");
const { useDirectusFetch, dFetch } = useDirectus();
const toast = useToast();

// Helper function to handle null, undefined, and empty strings
const displayValue = (value: string | null | undefined): string => {
  if (value === null || value === undefined) return '-';
  const trimmed = value.toString().trim();
  return trimmed === '' ? '-' : trimmed;

};

// Helper function to format dates
const formatDate = (dateString: string | Date | null | undefined): string => {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-';
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return '-';
  }
};

// Helper function to format numbers with commas
const formatNumber = (num: number | null | undefined): string => {
  if (num === null || num === undefined) return '-';
  return num.toLocaleString('ja-JP');
}; 

const { data: car } = await useDirectusFetch<UsedCars>(
  `/items/used_cars/${route.params.id}`,
  { server: false, key: `car-${route.params.id}` }
);


const visibleRef = ref(false);
const indexRef = ref(0);
const isLoading = ref(false);

// Utility function to validate image URLs
function isValidImageUrl(url: string): boolean {
  if (!url || typeof url !== "string") return false;

  // Remove whitespace
  url = url.trim();

  // Check if empty after trimming
  if (!url) return false;

  // Check for basic URL structure
  // Allow both http/https and protocol-relative URLs (//)
  const urlPattern = /^(https?:\/\/|\/\/)[^\s<>"{}|\\^`[\]]+$/i;
  if (!urlPattern.test(url)) return false;

  // Filter out known problematic patterns
  const problematicPatterns = [
    /animation_M\.gif/i,
    /placeholder/i,
    /no-image/i,
    /noimage/i,
    /loading/i,
    /spinner/i,
    /blank\.(jpg|png|gif)/i,
  ];

  for (const pattern of problematicPatterns) {
    if (pattern.test(url)) return false;
  }

  return true;
}

const images = computed(() => {
  const imageUrls = car.value?.image?.split(",") ?? [];
  return imageUrls
    .map((url) => url.trim())
    .filter((url) => isValidImageUrl(url));
});

const carousel = useTemplateRef("carousel");
const activeIndex = ref(0);

function onClickPrev() {
  carousel.value?.emblaApi?.scrollPrev();
}
function onClickNext() {
  carousel.value?.emblaApi?.scrollNext();
}
function onSelect(index: number) {
  activeIndex.value = index;
}

function select(index: number) {
  activeIndex.value = index;
  carousel.value?.emblaApi?.scrollTo(index);
  indexRef.value = index;
}

const deleteCar = async () => {
  isLoading.value = true;
  try {
    await dFetch(`/items/used_cars/${route.params.id}`, {
      method: "DELETE",
    });
    toast.add({
      title: "削除完了",
      description: "車両を削除しました",
      color: "success",
    });
    await navigateTo("/cars");
  } catch (error) {
    toast.add({
      title: "削除エラー",
      description: "車両の削除に失敗しました",
      color: "error",
    });
    console.error("Failed to delete car:", error);
  }
  isLoading.value = false;
};
</script>
